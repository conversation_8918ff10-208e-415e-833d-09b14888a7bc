/**
 * Centralized Data Storage Manager for PlomDesign
 * Provides a unified interface for all data storage operations
 * with organized folder structure and enhanced functionality
 */

// Centralized storage configuration
export const STORAGE_CONFIG = {
  // Main application data folder
  APP_DATA_FOLDER: 'PlomDesign_Data',
  
  // Organized subfolder structure
  FOLDERS: {
    WORKSPACE: 'workspace',
    BACKUPS: 'backups',
    EXPORTS: 'exports',
    IMPORTS: 'imports',
    SETTINGS: 'settings',
    CACHE: 'cache',
    LOGS: 'logs'
  },
  
  // File naming conventions
  FILE_PATTERNS: {
    WORKSPACE: 'workspace_{timestamp}.json',
    BACKUP: 'backup_{timestamp}.json',
    EXPORT: 'export_{timestamp}.json',
    SETTINGS: 'settings_{timestamp}.json',
    LOG: 'log_{date}.txt'
  },
  
  // Storage limits and quotas
  LIMITS: {
    MAX_BACKUPS: 20,
    MAX_EXPORTS: 10,
    MAX_LOG_FILES: 7,
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
    CLEANUP_THRESHOLD: 0.8 // 80% of quota
  }
};

// Enhanced storage keys with organized structure
export const ENHANCED_STORAGE_KEYS = {
  // Core application data
  WORKSPACE_DATA: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.WORKSPACE}/current`,
  CANVAS_STATE: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.WORKSPACE}/canvas`,
  CONNECTIONS: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.WORKSPACE}/connections`,
  
  // User preferences and settings
  USER_PREFERENCES: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.SETTINGS}/preferences`,
  APP_SETTINGS: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.SETTINGS}/application`,
  STORAGE_SETTINGS: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.SETTINGS}/storage`,
  
  // Metadata and tracking
  METADATA: `${STORAGE_CONFIG.APP_DATA_FOLDER}/metadata`,
  LAST_SAVE: `${STORAGE_CONFIG.APP_DATA_FOLDER}/last_save`,
  SESSION_INFO: `${STORAGE_CONFIG.APP_DATA_FOLDER}/session_info`,
  
  // Backup management
  BACKUP_INDEX: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.BACKUPS}/index`,
  
  // Cache management
  IMAGE_CACHE: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.CACHE}/images`,
  PRODUCT_CACHE: `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.CACHE}/products`
};

/**
 * Centralized Data Storage Manager Class
 */
export class CentralizedDataStorage {
  constructor() {
    this.isSupported = this.checkStorageSupport();
    this.quotaWarningThreshold = 0.8;
    this.initialized = false;
  }

  /**
   * Check if storage is supported
   */
  checkStorageSupport() {
    try {
      const test = 'storage_test';
      localStorage.setItem(test, test);
      localStorage.removeItem(test);
      return true;
    } catch (e) {
      console.warn('Storage not supported:', e);
      return false;
    }
  }

  /**
   * Initialize the centralized storage system
   */
  async initialize() {
    if (!this.isSupported) {
      console.warn('Storage not supported, skipping initialization');
      return false;
    }

    try {
      // Create folder structure metadata
      await this.createFolderStructure();
      
      // Initialize session tracking
      await this.initializeSession();
      
      // Perform cleanup if needed
      await this.performMaintenanceCleanup();
      
      this.initialized = true;
      console.log('Centralized data storage initialized successfully');
      return true;
    } catch (error) {
      console.error('Failed to initialize centralized storage:', error);
      return false;
    }
  }

  /**
   * Create virtual folder structure in localStorage
   */
  async createFolderStructure() {
    const folderStructure = {
      created: new Date().toISOString(),
      version: '1.0.0',
      folders: Object.values(STORAGE_CONFIG.FOLDERS),
      lastUpdated: new Date().toISOString()
    };

    this.setItem('folder_structure', folderStructure);
  }

  /**
   * Initialize session tracking
   */
  async initializeSession() {
    const sessionInfo = this.getItem(ENHANCED_STORAGE_KEYS.SESSION_INFO) || {
      totalSessions: 0,
      firstSession: new Date().toISOString(),
      lastSession: null
    };

    sessionInfo.totalSessions += 1;
    sessionInfo.lastSession = new Date().toISOString();
    sessionInfo.currentSessionStart = new Date().toISOString();

    this.setItem(ENHANCED_STORAGE_KEYS.SESSION_INFO, sessionInfo);
  }

  /**
   * Enhanced data saving with folder organization
   */
  async saveData(key, data, options = {}) {
    if (!this.isSupported) {
      console.warn('Storage not supported, data not saved');
      return { success: false, error: 'Storage not supported' };
    }

    try {
      // Add metadata to data
      const enhancedData = {
        ...data,
        metadata: {
          ...data.metadata,
          savedAt: new Date().toISOString(),
          version: '1.0.0',
          folder: this.getFolderFromKey(key),
          sessionId: this.getCurrentSessionId(),
          ...options.metadata
        }
      };

      // Check storage quota before saving
      const quotaCheck = await this.checkStorageQuota(JSON.stringify(enhancedData));
      if (!quotaCheck.canSave) {
        return { success: false, error: quotaCheck.error };
      }

      // Save the data
      this.setItem(key, enhancedData);
      
      // Update last save timestamp
      this.setItem(ENHANCED_STORAGE_KEYS.LAST_SAVE, new Date().toISOString());
      
      // Create backup if requested
      if (options.createBackup) {
        await this.createBackup(key, enhancedData);
      }

      return { 
        success: true, 
        message: 'Data saved successfully',
        location: this.getFolderFromKey(key),
        size: JSON.stringify(enhancedData).length
      };
    } catch (error) {
      console.error('Error saving data:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Enhanced data loading with validation
   */
  async loadData(key, defaultValue = null) {
    if (!this.isSupported) {
      console.warn('Storage not supported, returning default value');
      return { success: false, data: defaultValue, error: 'Storage not supported' };
    }

    try {
      const stored = this.getItem(key);
      if (!stored) {
        return { success: false, data: defaultValue, error: 'No data found' };
      }

      // Validate data structure
      const validation = this.validateDataStructure(stored);
      if (!validation.isValid) {
        console.warn(`Invalid data structure for ${key}:`, validation.errors);
        return { success: false, data: defaultValue, error: 'Invalid data structure' };
      }

      return { 
        success: true, 
        data: stored,
        metadata: stored.metadata,
        loadedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error loading data:', error);
      return { success: false, data: defaultValue, error: error.message };
    }
  }

  /**
   * Create organized backup with timestamp
   */
  async createBackup(sourceKey, data) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupKey = `${STORAGE_CONFIG.APP_DATA_FOLDER}/${STORAGE_CONFIG.FOLDERS.BACKUPS}/backup_${timestamp}`;
    
    const backupData = {
      ...data,
      backupInfo: {
        originalKey: sourceKey,
        createdAt: new Date().toISOString(),
        type: 'automatic',
        version: '1.0.0'
      }
    };

    const result = await this.saveData(backupKey, backupData);
    
    if (result.success) {
      // Update backup index
      await this.updateBackupIndex(backupKey, backupData.backupInfo);
      
      // Clean old backups
      await this.cleanOldBackups();
    }

    return result;
  }

  /**
   * Get folder name from storage key
   */
  getFolderFromKey(key) {
    for (const [folderName, folderPath] of Object.entries(STORAGE_CONFIG.FOLDERS)) {
      if (key.includes(folderPath)) {
        return folderName;
      }
    }
    return 'ROOT';
  }

  /**
   * Get current session ID
   */
  getCurrentSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Basic localStorage wrapper methods
   */
  setItem(key, data) {
    localStorage.setItem(key, JSON.stringify(data));
  }

  getItem(key) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error parsing stored data for key ${key}:`, error);
      return null;
    }
  }

  removeItem(key) {
    localStorage.removeItem(key);
  }

  /**
   * Validate data structure
   */
  validateDataStructure(data) {
    const errors = [];
    
    if (!data || typeof data !== 'object') {
      errors.push('Data must be an object');
    }
    
    if (data && !data.metadata) {
      errors.push('Missing metadata');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Check storage quota and available space
   */
  async checkStorageQuota(dataString) {
    try {
      const dataSize = new Blob([dataString]).size;
      const currentUsage = this.getStorageUsage();
      
      // Estimate available space (localStorage typically 5-10MB)
      const estimatedQuota = 5 * 1024 * 1024; // 5MB conservative estimate
      const availableSpace = estimatedQuota - currentUsage.totalSize;
      
      if (dataSize > availableSpace) {
        return {
          canSave: false,
          error: 'Insufficient storage space',
          required: dataSize,
          available: availableSpace
        };
      }
      
      return { canSave: true };
    } catch (error) {
      console.warn('Could not check storage quota:', error);
      return { canSave: true }; // Allow saving if quota check fails
    }
  }

  /**
   * Get current storage usage statistics
   */
  getStorageUsage() {
    let totalSize = 0;
    let itemCount = 0;
    const folderSizes = {};

    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      const value = localStorage.getItem(key);
      const size = new Blob([value]).size;
      
      totalSize += size;
      itemCount++;
      
      const folder = this.getFolderFromKey(key);
      folderSizes[folder] = (folderSizes[folder] || 0) + size;
    }

    return {
      totalSize,
      itemCount,
      folderSizes,
      formattedSize: this.formatBytes(totalSize)
    };
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Placeholder methods for future implementation
   */
  async updateBackupIndex(backupKey, backupInfo) {
    // Implementation for backup index management
  }

  async cleanOldBackups() {
    // Implementation for cleaning old backups
  }

  async performMaintenanceCleanup() {
    // Implementation for maintenance cleanup
  }
}

// Export singleton instance
export const centralizedStorage = new CentralizedDataStorage();
