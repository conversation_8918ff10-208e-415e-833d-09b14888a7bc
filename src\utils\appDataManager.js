/**
 * App Data Manager for PlomDesign
 * Handles automatic creation and management of the app's data storage folder
 * Creates a dedicated folder structure within the app directory for organized data storage
 */

import { STORAGE_CONFIG } from './centralizedDataStorage';

class AppDataManager {
  constructor() {
    this.appDataPath = null;
    this.isInitialized = false;
    this.folderStructure = {
      root: 'PlomDesign_AppData',
      subfolders: [
        'workspace',
        'backups', 
        'exports',
        'imports',
        'settings',
        'cache',
        'logs',
        'temp'
      ]
    };
    
    // Initialize on construction
    this.initializeAppDataFolder();
  }

  /**
   * Initialize the app data folder structure
   */
  async initializeAppDataFolder() {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        console.warn('AppDataManager: Not in browser environment');
        return false;
      }

      // Create the main app data folder path
      this.appDataPath = this.folderStructure.root;
      
      // Try to create folder structure using File System Access API if available
      if ('showDirectoryPicker' in window) {
        await this.createFileSystemStructure();
      } else {
        // Fallback to localStorage-based structure
        await this.createLocalStorageStructure();
      }

      this.isInitialized = true;
      console.log('AppDataManager: Successfully initialized app data folder structure');
      return true;

    } catch (error) {
      console.error('AppDataManager: Failed to initialize app data folder:', error);
      // Fallback to localStorage
      await this.createLocalStorageStructure();
      this.isInitialized = true;
      return false;
    }
  }

  /**
   * Create folder structure using File System Access API
   */
  async createFileSystemStructure() {
    try {
      // This would be used when the user grants directory access
      // For now, we'll prepare the structure for when it's available
      console.log('AppDataManager: File System Access API available, preparing structure');
      
      // Store the intended structure in localStorage for reference
      const folderStructure = {
        created: new Date().toISOString(),
        path: this.appDataPath,
        subfolders: this.folderStructure.subfolders,
        status: 'file-system-ready'
      };
      
      localStorage.setItem('plom_app_data_structure', JSON.stringify(folderStructure));
      return true;

    } catch (error) {
      console.warn('AppDataManager: File system structure creation failed:', error);
      throw error;
    }
  }

  /**
   * Create folder structure using localStorage
   */
  async createLocalStorageStructure() {
    try {
      console.log('AppDataManager: Creating localStorage-based folder structure');
      
      // Create virtual folder structure in localStorage
      const folderStructure = {
        created: new Date().toISOString(),
        path: this.appDataPath,
        subfolders: this.folderStructure.subfolders,
        status: 'localStorage',
        folders: {}
      };

      // Initialize each subfolder
      this.folderStructure.subfolders.forEach(folder => {
        folderStructure.folders[folder] = {
          created: new Date().toISOString(),
          files: [],
          size: 0
        };
        
        // Create localStorage key for each folder
        localStorage.setItem(`plom_folder_${folder}`, JSON.stringify({
          created: new Date().toISOString(),
          files: []
        }));
      });

      localStorage.setItem('plom_app_data_structure', JSON.stringify(folderStructure));
      
      // Create initial app info file
      await this.saveAppInfo();
      
      return true;

    } catch (error) {
      console.error('AppDataManager: localStorage structure creation failed:', error);
      throw error;
    }
  }

  /**
   * Save app information and initialization data
   */
  async saveAppInfo() {
    try {
      const appInfo = {
        appName: 'PlomDesign',
        version: '1.0.0',
        initialized: new Date().toISOString(),
        dataStructure: this.folderStructure,
        features: {
          autoSave: true,
          autoLoad: true,
          backupOnSave: true,
          enhancedStorage: 'showDirectoryPicker' in window
        },
        lastAccess: new Date().toISOString()
      };

      // Save to the settings folder
      await this.saveToFolder('settings', 'app_info.json', appInfo);
      
      return true;
    } catch (error) {
      console.error('AppDataManager: Failed to save app info:', error);
      return false;
    }
  }

  /**
   * Save data to a specific folder
   */
  async saveToFolder(folderName, fileName, data) {
    try {
      if (!this.folderStructure.subfolders.includes(folderName)) {
        throw new Error(`Invalid folder name: ${folderName}`);
      }

      const key = `plom_${folderName}_${fileName}`;
      const fileData = {
        fileName,
        folder: folderName,
        data,
        saved: new Date().toISOString(),
        size: JSON.stringify(data).length
      };

      localStorage.setItem(key, JSON.stringify(fileData));
      
      // Update folder index
      await this.updateFolderIndex(folderName, fileName, fileData.size);
      
      return { success: true, path: key, size: fileData.size };

    } catch (error) {
      console.error(`AppDataManager: Failed to save to folder ${folderName}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Load data from a specific folder
   */
  async loadFromFolder(folderName, fileName) {
    try {
      if (!this.folderStructure.subfolders.includes(folderName)) {
        throw new Error(`Invalid folder name: ${folderName}`);
      }

      const key = `plom_${folderName}_${fileName}`;
      const stored = localStorage.getItem(key);
      
      if (!stored) {
        return { success: false, error: 'File not found' };
      }

      const fileData = JSON.parse(stored);
      return { 
        success: true, 
        data: fileData.data, 
        metadata: {
          fileName: fileData.fileName,
          folder: fileData.folder,
          saved: fileData.saved,
          size: fileData.size
        }
      };

    } catch (error) {
      console.error(`AppDataManager: Failed to load from folder ${folderName}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update folder index with file information
   */
  async updateFolderIndex(folderName, fileName, fileSize) {
    try {
      const folderKey = `plom_folder_${folderName}`;
      const folderData = JSON.parse(localStorage.getItem(folderKey) || '{"files": []}');
      
      // Remove existing file entry if it exists
      folderData.files = folderData.files.filter(f => f.name !== fileName);
      
      // Add new file entry
      folderData.files.push({
        name: fileName,
        size: fileSize,
        updated: new Date().toISOString()
      });

      localStorage.setItem(folderKey, JSON.stringify(folderData));
      
    } catch (error) {
      console.error('AppDataManager: Failed to update folder index:', error);
    }
  }

  /**
   * Get folder contents
   */
  async getFolderContents(folderName) {
    try {
      if (!this.folderStructure.subfolders.includes(folderName)) {
        throw new Error(`Invalid folder name: ${folderName}`);
      }

      const folderKey = `plom_folder_${folderName}`;
      const folderData = JSON.parse(localStorage.getItem(folderKey) || '{"files": []}');
      
      return {
        success: true,
        folder: folderName,
        files: folderData.files || [],
        totalFiles: folderData.files?.length || 0,
        totalSize: folderData.files?.reduce((sum, f) => sum + (f.size || 0), 0) || 0
      };

    } catch (error) {
      console.error(`AppDataManager: Failed to get folder contents for ${folderName}:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get app data folder status
   */
  getStatus() {
    try {
      const structure = localStorage.getItem('plom_app_data_structure');
      if (!structure) {
        return { initialized: false, error: 'No structure found' };
      }

      const data = JSON.parse(structure);
      return {
        initialized: this.isInitialized,
        path: this.appDataPath,
        created: data.created,
        status: data.status,
        subfolders: this.folderStructure.subfolders,
        available: true
      };

    } catch (error) {
      return { initialized: false, error: error.message };
    }
  }

  /**
   * Clean up old files and manage storage
   */
  async cleanup() {
    try {
      console.log('AppDataManager: Starting cleanup process');
      
      // Clean up temp folder
      await this.cleanupFolder('temp', 24 * 60 * 60 * 1000); // 24 hours
      
      // Clean up old logs
      await this.cleanupFolder('logs', 7 * 24 * 60 * 60 * 1000); // 7 days
      
      // Limit backups
      await this.limitFolderFiles('backups', 20);
      
      console.log('AppDataManager: Cleanup completed');
      return true;

    } catch (error) {
      console.error('AppDataManager: Cleanup failed:', error);
      return false;
    }
  }

  /**
   * Clean up files older than specified age
   */
  async cleanupFolder(folderName, maxAge) {
    try {
      const contents = await this.getFolderContents(folderName);
      if (!contents.success) return;

      const now = Date.now();
      const filesToDelete = contents.files.filter(file => {
        const fileAge = now - new Date(file.updated).getTime();
        return fileAge > maxAge;
      });

      for (const file of filesToDelete) {
        const key = `plom_${folderName}_${file.name}`;
        localStorage.removeItem(key);
      }

      // Update folder index
      const folderKey = `plom_folder_${folderName}`;
      const folderData = JSON.parse(localStorage.getItem(folderKey) || '{"files": []}');
      folderData.files = folderData.files.filter(f => 
        !filesToDelete.some(df => df.name === f.name)
      );
      localStorage.setItem(folderKey, JSON.stringify(folderData));

    } catch (error) {
      console.error(`AppDataManager: Failed to cleanup folder ${folderName}:`, error);
    }
  }

  /**
   * Limit number of files in a folder
   */
  async limitFolderFiles(folderName, maxFiles) {
    try {
      const contents = await this.getFolderContents(folderName);
      if (!contents.success || contents.files.length <= maxFiles) return;

      // Sort by date and keep only the newest files
      const sortedFiles = contents.files.sort((a, b) => 
        new Date(b.updated).getTime() - new Date(a.updated).getTime()
      );

      const filesToDelete = sortedFiles.slice(maxFiles);

      for (const file of filesToDelete) {
        const key = `plom_${folderName}_${file.name}`;
        localStorage.removeItem(key);
      }

      // Update folder index
      const folderKey = `plom_folder_${folderName}`;
      const folderData = JSON.parse(localStorage.getItem(folderKey) || '{"files": []}');
      folderData.files = sortedFiles.slice(0, maxFiles);
      localStorage.setItem(folderKey, JSON.stringify(folderData));

    } catch (error) {
      console.error(`AppDataManager: Failed to limit files in folder ${folderName}:`, error);
    }
  }
}

// Create and export singleton instance
const appDataManager = new AppDataManager();
export default appDataManager;
