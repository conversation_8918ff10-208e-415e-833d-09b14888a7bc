# Centralized Data Storage System

## Overview
The Centralized Data Storage System provides a unified, organized approach to data management in PlomDesign. It replaces the scattered localStorage usage with a structured, folder-based system that offers better organization, automatic cleanup, and enhanced reliability.

## 🏗️ Architecture

### Folder Structure
```
PlomDesign_Data/
├── workspace/          # Current workspace and canvas data
│   ├── current         # Active workspace state
│   ├── canvas          # Canvas layout and products
│   └── connections     # Product connections
├── backups/           # Automatic timestamped backups
│   ├── backup_2024-01-01T10-00-00  # Individual backup files
│   └── index          # Backup index for quick access
├── exports/           # User-exported data files
├── imports/           # Imported configurations
├── settings/          # Application and user preferences
│   ├── preferences    # User preferences
│   ├── application    # App settings
│   └── storage        # Storage configuration
├── cache/             # Temporary cached data
│   ├── images         # Product image cache
│   └── products       # Product data cache
└── logs/              # System logs and debugging info
```

### Storage Keys
```javascript
// Core application data
WORKSPACE_DATA: 'PlomDesign_Data/workspace/current'
CANVAS_STATE: 'PlomDesign_Data/workspace/canvas'
CONNECTIONS: 'PlomDesign_Data/workspace/connections'

// User preferences and settings
USER_PREFERENCES: 'PlomDesign_Data/settings/preferences'
APP_SETTINGS: 'PlomDesign_Data/settings/application'
STORAGE_SETTINGS: 'PlomDesign_Data/settings/storage'

// Metadata and tracking
METADATA: 'PlomDesign_Data/metadata'
LAST_SAVE: 'PlomDesign_Data/last_save'
SESSION_INFO: 'PlomDesign_Data/session_info'
```

## 🚀 Usage

### Basic Operations

#### Initialize Storage
```javascript
import { centralizedStorage } from '../utils/centralizedDataStorage';

// Initialize the storage system
const initialized = await centralizedStorage.initialize();
if (initialized) {
  console.log('Storage system ready');
}
```

#### Save Data
```javascript
// Save workspace data with automatic backup
const result = await centralizedStorage.saveData(
  'workspace_current',
  workspaceData,
  { createBackup: true }
);

if (result.success) {
  console.log('Data saved successfully');
}
```

#### Load Data
```javascript
// Load workspace data with validation
const result = await centralizedStorage.loadData('workspace_current');

if (result.success) {
  const workspaceData = result.data;
  console.log('Data loaded:', workspaceData);
}
```

#### Create Backup
```javascript
// Create manual backup
const backupResult = await centralizedStorage.createBackup(
  'workspace_current',
  workspaceData
);
```

### Advanced Features

#### Storage Usage Monitoring
```javascript
// Get current storage usage
const usage = centralizedStorage.getStorageUsage();
console.log(`Total size: ${usage.formattedSize}`);
console.log(`Items: ${usage.itemCount}`);
console.log(`Folder sizes:`, usage.folderSizes);
```

#### Quota Management
```javascript
// Check if data can be saved
const quotaCheck = await centralizedStorage.checkStorageQuota(dataString);
if (!quotaCheck.canSave) {
  console.warn('Insufficient storage space');
}
```

## 🔧 Configuration

### Storage Limits
```javascript
LIMITS: {
  MAX_BACKUPS: 20,           // Maximum number of backups to keep
  MAX_EXPORTS: 10,           // Maximum number of export files
  MAX_LOG_FILES: 7,          // Maximum number of log files
  MAX_FILE_SIZE: 50 * 1024 * 1024,  // 50MB max file size
  CLEANUP_THRESHOLD: 0.8     // Cleanup when 80% of quota is used
}
```

### File Naming Patterns
```javascript
FILE_PATTERNS: {
  WORKSPACE: 'workspace_{timestamp}.json',
  BACKUP: 'backup_{timestamp}.json',
  EXPORT: 'export_{timestamp}.json',
  SETTINGS: 'settings_{timestamp}.json',
  LOG: 'log_{date}.txt'
}
```

## 🛡️ Data Safety Features

### Automatic Backups
- **Timestamp-based naming** for easy identification
- **Automatic rotation** to prevent storage overflow
- **Metadata tracking** for backup management
- **Integrity validation** before backup creation

### Data Validation
- **Structure validation** to ensure data integrity
- **Version compatibility** checking
- **Error recovery** with fallback mechanisms
- **Corruption detection** and handling

### Session Tracking
- **Usage statistics** for monitoring
- **Session information** for debugging
- **Performance metrics** for optimization
- **Error logging** for troubleshooting

## 🔄 Migration from Legacy Storage

### Automatic Migration
The system automatically detects and migrates data from the old localStorage structure:

```javascript
// Old keys are automatically migrated to new structure
'plom_canvas_state' → 'PlomDesign_Data/workspace/canvas'
'plom_products' → 'PlomDesign_Data/workspace/products'
'plom_settings' → 'PlomDesign_Data/settings/application'
```

### Backward Compatibility
- **Legacy key support** during transition period
- **Gradual migration** to avoid data loss
- **Fallback mechanisms** for unsupported features
- **Data preservation** throughout the migration process

## 📊 Monitoring and Debugging

### Storage Analytics
```javascript
// Get detailed storage information
const analytics = {
  totalSize: usage.totalSize,
  folderBreakdown: usage.folderSizes,
  itemCount: usage.itemCount,
  lastCleanup: getLastCleanupTime(),
  quotaUsage: (usage.totalSize / estimatedQuota) * 100
};
```

### Debug Information
```javascript
// Enable debug mode for detailed logging
centralizedStorage.debugMode = true;

// Get system status
const status = centralizedStorage.getSystemStatus();
console.log('Storage system status:', status);
```

## 🚨 Error Handling

### Common Error Scenarios
1. **Storage quota exceeded** - Automatic cleanup triggered
2. **Data corruption detected** - Fallback to backup data
3. **Browser storage disabled** - Graceful degradation
4. **Network issues** - Retry mechanisms for cloud features

### Error Recovery
```javascript
// Automatic error recovery
try {
  await centralizedStorage.saveData(key, data);
} catch (error) {
  // Automatic fallback to legacy storage
  console.warn('Falling back to legacy storage:', error);
  localStorage.setItem(key, JSON.stringify(data));
}
```

## 🔮 Future Enhancements

### Planned Features
- **Cloud storage integration** for cross-device sync
- **Data compression** for better space utilization
- **Real-time synchronization** between browser tabs
- **Advanced analytics** and usage reporting
- **Custom storage backends** for enterprise users

### Extensibility
The system is designed to be easily extensible:
- **Plugin architecture** for custom storage providers
- **Event system** for monitoring storage operations
- **Custom validation rules** for specific data types
- **Configurable cleanup policies** for different use cases

## 📝 Best Practices

### Performance
- **Use debounced saves** to avoid excessive writes
- **Implement lazy loading** for large datasets
- **Cache frequently accessed data** in memory
- **Monitor storage usage** regularly

### Data Management
- **Regular backups** before major operations
- **Validate data** before saving
- **Use meaningful keys** for better organization
- **Clean up unused data** periodically

### Error Handling
- **Always check return values** from storage operations
- **Implement fallback mechanisms** for critical data
- **Log errors** for debugging purposes
- **Provide user feedback** for storage issues

This centralized storage system provides a robust foundation for data management in PlomDesign, ensuring reliability, performance, and scalability for future enhancements.
