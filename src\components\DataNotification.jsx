import React, { useState, useEffect } from 'react';
import { CheckCircle, AlertCircle, Info, X, Download, Upload, Save, Database } from 'lucide-react';

const DataNotification = ({ 
  type = 'info', // 'success', 'error', 'info', 'warning'
  message, 
  details = null,
  duration = 4000,
  onClose,
  action = null, // 'save', 'load', 'export', 'import', 'backup'
  persistent = false
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    if (!persistent && duration > 0) {
      const timer = setTimeout(() => {
        handleClose();
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [duration, persistent]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      setIsVisible(false);
      if (onClose) onClose();
    }, 300);
  };

  const getIcon = () => {
    if (action) {
      switch (action) {
        case 'save':
          return <Save className="w-5 h-5" />;
        case 'load':
          return <Database className="w-5 h-5" />;
        case 'export':
          return <Download className="w-5 h-5" />;
        case 'import':
          return <Upload className="w-5 h-5" />;
        case 'backup':
          return <Database className="w-5 h-5" />;
        default:
          break;
      }
    }

    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5" />;
      case 'error':
        return <AlertCircle className="w-5 h-5" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5" />;
      default:
        return <Info className="w-5 h-5" />;
    }
  };

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: 'bg-green-500/10 border-green-500/20',
          text: 'text-green-400',
          icon: 'text-green-500'
        };
      case 'error':
        return {
          bg: 'bg-red-500/10 border-red-500/20',
          text: 'text-red-400',
          icon: 'text-red-500'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-500/10 border-yellow-500/20',
          text: 'text-yellow-400',
          icon: 'text-yellow-500'
        };
      default:
        return {
          bg: 'bg-blue-500/10 border-blue-500/20',
          text: 'text-blue-400',
          icon: 'text-blue-500'
        };
    }
  };

  const colors = getColors();

  if (!isVisible) return null;

  return (
    <div 
      className={`fixed top-4 right-4 z-[9998] transform transition-all duration-300 ease-out ${
        isExiting ? 'translate-x-full opacity-0' : 'translate-x-0 opacity-100'
      }`}
    >
      <div className={`${colors.bg} border rounded-lg p-4 max-w-sm shadow-lg backdrop-blur-sm`}>
        <div className="flex items-start space-x-3">
          {/* Icon */}
          <div className={`${colors.icon} flex-shrink-0 mt-0.5`}>
            {getIcon()}
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            <p className={`${colors.text} text-sm font-medium`}>
              {message}
            </p>
            
            {details && (
              <p className="text-dark-400 text-xs mt-1">
                {typeof details === 'string' ? details : JSON.stringify(details)}
              </p>
            )}
          </div>

          {/* Close Button */}
          <button
            onClick={handleClose}
            className="flex-shrink-0 text-dark-400 hover:text-white transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        {/* Progress bar for auto-close */}
        {!persistent && duration > 0 && (
          <div className="mt-3 w-full bg-dark-700/50 rounded-full h-1">
            <div 
              className={`h-1 rounded-full transition-all ease-linear ${
                type === 'success' ? 'bg-green-500' :
                type === 'error' ? 'bg-red-500' :
                type === 'warning' ? 'bg-yellow-500' :
                'bg-blue-500'
              }`}
              style={{ 
                width: '100%',
                animation: `shrink ${duration}ms linear forwards`
              }}
            ></div>
          </div>
        )}
      </div>

      <style jsx>{`
        @keyframes shrink {
          from { width: 100%; }
          to { width: 0%; }
        }
      `}</style>
    </div>
  );
};

// Notification Manager Component
export const DataNotificationManager = () => {
  const [notifications, setNotifications] = useState([]);

  const addNotification = (notification) => {
    // Enhanced debugging for "Work restored" messages
    if (notification && notification.message && notification.message.includes('Work restored')) {
      console.log('🔍 NOTIFICATION DEBUG: Work restored notification triggered');
      console.log('📝 Message:', notification.message);
      console.log('📍 Stack trace:');
      console.trace();
      console.log('🕐 Timestamp:', new Date().toISOString());
      console.log('📊 Current notifications count:', notifications.length);
    }

    // Enhanced duplicate detection - check for identical messages within 5 seconds
    const now = Date.now();
    const duplicateWindow = 5000; // 5 seconds

    const existingNotification = notifications.find(existing =>
      existing.message === notification.message &&
      (now - existing.id) < duplicateWindow
    );

    if (existingNotification) {
      const timeSinceLast = now - existingNotification.id;
      console.log('🚫 DUPLICATE PREVENTED:', notification.message);
      console.log('⏰ Time since last:', timeSinceLast, 'ms');
      console.log('🔄 Extending duration of existing notification');

      // Extend the duration of the existing notification instead of creating a new one
      return existingNotification.id;
    }

    const id = now + Math.random();
    const newNotification = { ...notification, id };

    console.log('✅ NOTIFICATION ADDED:', notification.message);
    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove if not persistent
    if (!notification.persistent) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration || 4000);
    }

    return id;
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Expose methods globally
  React.useEffect(() => {
    window.showDataNotification = addNotification;
    window.hideDataNotification = removeNotification;

    return () => {
      delete window.showDataNotification;
      delete window.hideDataNotification;
    };
  }, []);

  return (
    <div className="fixed top-4 right-4 z-[9998] space-y-2">
      {notifications.map((notification) => (
        <DataNotification
          key={notification.id}
          {...notification}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
};

export default DataNotification;
