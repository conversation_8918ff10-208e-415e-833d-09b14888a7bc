import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { dataStorage, STORAGE_KEYS, DEFAULT_DATA } from '../utils/dataStorage';
import { StreamingJSONParser, ChunkedDataProcessor, MemoryMonitor } from '../utils/streamingParser';
import LargeDatasetManager from '../utils/largeDatasetManager';
import enhancedStorageManager from '../utils/enhancedStorageManager';
import appDataManager from '../utils/appDataManager';

const DataContext = createContext();

export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export const DataProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [loadingMessage, setLoadingMessage] = useState('Initializing...');
  const [hasLoadedData, setHasLoadedData] = useState(false);
  const [autoSaveEnabled, setAutoSaveEnabled] = useState(true);
  const [lastSaveTime, setLastSaveTime] = useState(null);
  const [storageInfo, setStorageInfo] = useState(null);

  // Enhanced storage management state
  const [enhancedStorageStatus, setEnhancedStorageStatus] = useState(null);
  const [autoRestoreStatus, setAutoRestoreStatus] = useState(null);
  const [storageLocation, setStorageLocation] = useState('localStorage');

  // Large dataset management state
  const [isLargeDataset, setIsLargeDataset] = useState(false);
  const [datasetMetrics, setDatasetMetrics] = useState({});
  const [processingProgress, setProcessingProgress] = useState(0);
  const [memoryUsage, setMemoryUsage] = useState({});

  // References for large dataset management
  const autoSaveIntervalRef = useRef(null);
  const datasetManagerRef = useRef(null);
  const streamingParserRef = useRef(null);
  const memoryMonitorRef = useRef(null);
  const chunkedProcessorRef = useRef(null);
  const loadInitiatedRef = useRef(false);

  /**
   * Initialize large dataset management tools
   */
  const initializeLargeDatasetTools = useCallback(() => {
    // Initialize dataset manager
    if (!datasetManagerRef.current) {
      datasetManagerRef.current = new LargeDatasetManager({
        maxMemoryUsage: 0.8,
        chunkSize: 10000,
        indexingEnabled: true
      });
    }

    // Initialize memory monitor
    if (!memoryMonitorRef.current) {
      memoryMonitorRef.current = new MemoryMonitor({
        warningThreshold: 0.7,
        criticalThreshold: 0.85,
        onWarning: (memory, ratio) => {
          console.warn(`Memory usage warning: ${Math.round(ratio * 100)}%`);
          setMemoryUsage({ ...memory, ratio, status: 'warning' });
        },
        onCritical: (memory, ratio) => {
          console.error(`Critical memory usage: ${Math.round(ratio * 100)}%`);
          setMemoryUsage({ ...memory, ratio, status: 'critical' });
          // Trigger cleanup
          if (datasetManagerRef.current) {
            datasetManagerRef.current.performMemoryCleanup();
          }
        }
      });
      memoryMonitorRef.current.startMonitoring();
    }

    // Initialize chunked processor
    if (!chunkedProcessorRef.current) {
      chunkedProcessorRef.current = new ChunkedDataProcessor({
        chunkSize: 1000,
        onProgress: (progress, processed, total) => {
          setProcessingProgress(progress);
        },
        delay: 10
      });
    }
  }, []);

  /**
   * Load all application data on startup with enhanced storage auto-restore
   */
  const loadApplicationData = useCallback(async () => {
    setIsLoading(true);
    setLoadingMessage('Loading your previous work...');

    try {
      // Initialize app data manager first
      setLoadingMessage('Setting up app data storage...');
      const appDataStatus = appDataManager.getStatus();
      console.log('App data manager status:', appDataStatus);

      // Get enhanced storage status
      const storageStatus = await enhancedStorageManager.getStorageStatus();
      setEnhancedStorageStatus(storageStatus);
      setStorageLocation(storageStatus.currentMode);

      // Try to load from app data manager first
      setLoadingMessage('Checking app data storage...');
      const appDataResult = await appDataManager.loadFromFolder('workspace', 'current_workspace.json');

      if (appDataResult.success) {
        setLoadingMessage('Restoring workspace from app data storage...');

        setHasLoadedData(true);
        setAutoSaveEnabled(appDataResult.data.settings?.autoSave ?? true);
        setLastSaveTime(appDataResult.metadata.saved || new Date().toISOString());

        // Update storage info
        setStorageInfo({
          formattedSize: `${Math.round(appDataResult.metadata.size / 1024)} KB`,
          itemCount: 1,
          lastModified: appDataResult.metadata.saved,
          source: 'app-data-manager'
        });

        setAutoRestoreStatus({
          success: true,
          message: 'Data restored from app storage',
          location: 'app-data-storage',
          hasWorkspaceData: true
        });

        setLoadingMessage('App data restored successfully!');

        setTimeout(() => {
          setIsLoading(false);
        }, 500);

        return {
          success: true,
          data: appDataResult.data,
          message: 'Data restored from app storage',
          isAppDataRestore: true,
          location: 'app-data-storage'
        };
      }

      // Try enhanced auto-restore as fallback
      setLoadingMessage('Checking for enhanced storage data...');
      const autoRestoreResult = await enhancedStorageManager.autoRestoreOnStartup();

      if (autoRestoreResult.success) {
        setAutoRestoreStatus({
          success: true,
          message: autoRestoreResult.message,
          location: autoRestoreResult.location,
          hasWorkspaceData: !!autoRestoreResult.workspaceData,
          hasStorageMetadata: !!autoRestoreResult.storageMetadata
        });

        // If we have workspace data from enhanced storage, use it
        if (autoRestoreResult.workspaceData) {
          setLoadingMessage('Restoring workspace from enhanced storage...');

          // Update storage info from metadata if available
          if (autoRestoreResult.storageMetadata) {
            setStorageInfo(autoRestoreResult.storageMetadata);
          }

          setHasLoadedData(true);
          setAutoSaveEnabled(autoRestoreResult.workspaceData.settings?.autoSave ?? true);
          setLastSaveTime(autoRestoreResult.workspaceData.savedAt || new Date().toISOString());

          setLoadingMessage('Enhanced data restored successfully!');

          setTimeout(() => {
            setIsLoading(false);
          }, 500);

          return {
            success: true,
            data: autoRestoreResult.workspaceData,
            message: `Enhanced auto-restore successful from ${autoRestoreResult.location}`,
            isEnhancedRestore: true,
            location: autoRestoreResult.location
          };
        }
      } else {
        setAutoRestoreStatus({
          success: false,
          message: autoRestoreResult.message
        });
      }

      // Fallback to traditional loading
      setLoadingMessage('Loading from browser storage...');

      // Check if auto-loading is enabled
      const settings = dataStorage.loadData(STORAGE_KEYS.SETTINGS, DEFAULT_DATA.settings);

      if (!settings.autoLoad) {
        setIsLoading(false);
        setHasLoadedData(false);
        return { success: true, data: DEFAULT_DATA, message: 'Auto-loading disabled' };
      }

      // Load canvas state
      setLoadingMessage('Restoring canvas layout...');
      const canvasState = dataStorage.loadData(STORAGE_KEYS.CANVAS_STATE, {
        canvasProducts: [],
        connections: [],
        selectedProducts: []
      });

      // Load user preferences
      setLoadingMessage('Loading preferences...');
      const userPreferences = dataStorage.loadData(STORAGE_KEYS.USER_PREFERENCES, DEFAULT_DATA.userPreferences);

      // Load settings
      const loadedSettings = dataStorage.loadData(STORAGE_KEYS.SETTINGS, DEFAULT_DATA.settings);

      // Combine all data
      const loadedData = {
        ...canvasState,
        settings: loadedSettings,
        userPreferences,
        metadata: {
          ...DEFAULT_DATA.metadata,
          lastModified: new Date().toISOString(),
          totalSessions: (canvasState.metadata?.totalSessions || 0) + 1
        }
      };

      // Update storage info
      setStorageInfo(dataStorage.getStorageInfo());

      setHasLoadedData(true);
      setAutoSaveEnabled(loadedSettings.autoSave);
      setLastSaveTime(dataStorage.loadData(STORAGE_KEYS.LAST_SAVE));

      setLoadingMessage('Data loaded successfully!');
      
      // Small delay to show success message
      setTimeout(() => {
        setIsLoading(false);
      }, 500);

      return { 
        success: true, 
        data: loadedData, 
        message: 'Previous work restored successfully',
        isFirstTime: !canvasState.canvasProducts || canvasState.canvasProducts.length === 0
      };

    } catch (error) {
      console.error('Error loading application data:', error);
      setLoadingMessage('Error loading data, starting fresh...');
      
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);

      return { 
        success: false, 
        data: DEFAULT_DATA, 
        error: error.message,
        message: 'Started with fresh workspace'
      };
    }
  }, []);

  /**
   * Save application data with enhanced storage
   */
  const saveApplicationData = useCallback(async (data) => {
    try {
      const timestamp = new Date().toISOString();

      // Prepare comprehensive data for saving
      const dataToSave = {
        canvasProducts: data.canvasProducts || [],
        connections: data.connections || [],
        selectedProducts: data.selectedProducts || [],
        settings: data.settings || { autoSave: true, autoLoad: true },
        userPreferences: data.userPreferences || {},
        metadata: {
          ...data.metadata,
          lastModified: timestamp,
          version: '1.0.0',
          savedBy: 'app-data-manager'
        }
      };

      // Save to app data manager first (primary storage)
      const appDataResult = await appDataManager.saveToFolder('workspace', 'current_workspace.json', dataToSave);

      if (appDataResult.success) {
        console.log('Data saved to app data manager successfully');

        // Create automatic backup
        const backupFileName = `backup_${Date.now()}.json`;
        await appDataManager.saveToFolder('backups', backupFileName, {
          ...dataToSave,
          backupCreated: timestamp,
          originalSave: timestamp
        });

        setLastSaveTime(timestamp);

        // Update storage info with app data manager info
        setStorageInfo({
          formattedSize: `${Math.round(appDataResult.size / 1024)} KB`,
          itemCount: 1,
          lastModified: timestamp,
          source: 'app-data-manager'
        });
      }

      // Create backup in traditional storage before saving
      const currentData = dataStorage.loadData(STORAGE_KEYS.CANVAS_STATE);
      if (currentData && Object.keys(currentData).length > 0) {
        dataStorage.createBackup(currentData);
      }

      // Save to traditional storage as fallback
      const canvasData = {
        canvasProducts: dataToSave.canvasProducts,
        connections: dataToSave.connections,
        selectedProducts: dataToSave.selectedProducts,
        metadata: dataToSave.metadata
      };

      const traditionalSuccess = dataStorage.saveData(STORAGE_KEYS.CANVAS_STATE, canvasData);

      if (traditionalSuccess && !appDataResult.success) {
        setLastSaveTime(timestamp);
        const currentStorageInfo = dataStorage.getStorageInfo();
        setStorageInfo(currentStorageInfo);
      }

      // Try to save to enhanced storage as well
      try {
        const enhancedSaveResult = await enhancedStorageManager.saveWorkspaceData(canvasData);
        const metadataSaveResult = await enhancedStorageManager.saveStorageMetadata(storageInfo);

        if (enhancedSaveResult.success || metadataSaveResult.success) {
          console.log('Enhanced storage save successful:', {
            workspace: enhancedSaveResult.success,
            metadata: metadataSaveResult.success,
            location: enhancedSaveResult.location || metadataSaveResult.location
          });
        }
      } catch (enhancedError) {
        console.warn('Enhanced storage save failed, continuing with other storage methods:', enhancedError);
      }

      // Perform cleanup periodically (10% chance)
      if (Math.random() < 0.1) {
        appDataManager.cleanup();
      }

      const success = appDataResult.success || traditionalSuccess;
      const primaryStorage = appDataResult.success ? 'app-data-storage' : 'browser-storage';

      return {
        success,
        message: success
          ? `Data saved successfully to ${primaryStorage}`
          : 'Failed to save data to any storage method',
        timestamp,
        location: primaryStorage,
        enhancedStorageUsed: storageLocation !== 'localStorage',
        appDataManagerUsed: appDataResult.success
      };

    } catch (error) {
      console.error('Error saving application data:', error);
      return {
        success: false,
        message: 'Error saving data: ' + error.message,
        error: error.message
      };
    }
  }, [storageLocation, storageInfo]);

  /**
   * Save user preferences
   */
  const saveUserPreferences = useCallback((preferences) => {
    try {
      const success = dataStorage.saveData(STORAGE_KEYS.USER_PREFERENCES, preferences);
      return { success, message: success ? 'Preferences saved' : 'Failed to save preferences' };
    } catch (error) {
      console.error('Error saving preferences:', error);
      return { success: false, message: 'Error saving preferences: ' + error.message };
    }
  }, []);

  /**
   * Save application settings
   */
  const saveSettings = useCallback((settings) => {
    try {
      const success = dataStorage.saveData(STORAGE_KEYS.SETTINGS, settings);
      if (success) {
        setAutoSaveEnabled(settings.autoSave);
      }
      return { success, message: success ? 'Settings saved' : 'Failed to save settings' };
    } catch (error) {
      console.error('Error saving settings:', error);
      return { success: false, message: 'Error saving settings: ' + error.message };
    }
  }, []);

  /**
   * Export data to file with enhanced storage metadata
   */
  const exportData = useCallback(async (data, filename) => {
    try {
      // Get current storage info for enhanced export
      const currentStorageInfo = dataStorage.getStorageInfo();

      // Create enhanced export data with storage metadata
      const enhancedExportData = await enhancedStorageManager.exportWithStorageMetadata(data, currentStorageInfo);

      // Export using traditional method with enhanced data
      const success = dataStorage.exportToFile(enhancedExportData, filename);

      if (success) {
        return {
          success: true,
          message: 'Data exported successfully with enhanced storage metadata',
          enhancedExport: true,
          storageLocation: storageLocation
        };
      } else {
        return { success: false, message: 'Failed to export data' };
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      // Fallback to traditional export
      try {
        const success = dataStorage.exportToFile(data, filename);
        return {
          success,
          message: success ? 'Data exported successfully (fallback mode)' : 'Failed to export data',
          enhancedExport: false
        };
      } catch (fallbackError) {
        return { success: false, message: 'Error exporting data: ' + fallbackError.message };
      }
    }
  }, [storageLocation]);

  /**
   * Import data from file with enhanced large file support and streaming
   */
  const importData = useCallback(async (file, onProgress = null) => {
    try {
      // Initialize large dataset tools
      initializeLargeDatasetTools();

      // Log file size for monitoring
      const fileSizeMB = Math.round(file.size / (1024 * 1024));
      console.log(`Starting import of ${fileSizeMB}MB file: ${file.name}`);

      // Determine if this is a large dataset (>50MB or >10k products estimated)
      const isLargeFile = file.size > 50 * 1024 * 1024;
      setIsLargeDataset(isLargeFile);

      let importedData;

      if (isLargeFile) {
        // Use streaming parser for large files
        console.log('Using streaming parser for large file');

        if (!streamingParserRef.current) {
          streamingParserRef.current = new StreamingJSONParser({
            chunkSize: 10 * 1024 * 1024, // 10MB chunks
            onProgress: (progress, processed, total) => {
              setProcessingProgress(progress);
              if (onProgress) onProgress(progress, processed, total);
            },
            onComplete: (data) => {
              console.log('Streaming parse completed');
            },
            onError: (error) => {
              console.error('Streaming parse error:', error);
            }
          });
        }

        importedData = await streamingParserRef.current.parseFile(file);

        // Process with dataset manager for large datasets
        if (datasetManagerRef.current && importedData.canvasProducts?.length > 10000) {
          console.log('Processing large dataset with dataset manager');
          await datasetManagerRef.current.loadDataset(importedData);

          // Get performance metrics
          const metrics = datasetManagerRef.current.getPerformanceMetrics();
          setDatasetMetrics(metrics);
        }

      } else {
        // Use traditional import for smaller files
        importedData = await dataStorage.importFromFile(file);
      }

      console.log(`Successfully imported ${fileSizeMB}MB file with ${importedData.canvasProducts?.length || 0} products`);

      // Handle enhanced storage metadata if present
      let enhancedImportInfo = null;
      if (importedData.storageMetadata) {
        try {
          // Save the imported storage metadata to enhanced storage
          await enhancedStorageManager.saveStorageMetadata(importedData.storageMetadata);

          // Save the workspace data to enhanced storage
          await enhancedStorageManager.saveWorkspaceData(importedData);

          // Update local storage info
          setStorageInfo(importedData.storageMetadata);

          enhancedImportInfo = {
            hasEnhancedMetadata: true,
            exportedFrom: importedData.storageMetadata.exportedFrom,
            exportedAt: importedData.storageMetadata.exportedAt,
            restoredToEnhancedStorage: true
          };

          console.log('Enhanced storage metadata restored from import');
        } catch (enhancedError) {
          console.warn('Failed to restore enhanced storage metadata:', enhancedError);
          enhancedImportInfo = {
            hasEnhancedMetadata: true,
            restoredToEnhancedStorage: false,
            error: enhancedError.message
          };
        }
      }

      return {
        success: true,
        data: importedData,
        message: `Data imported successfully (${fileSizeMB}MB, ${importedData.canvasProducts?.length || 0} products)`,
        fileSize: file.size,
        productCount: importedData.canvasProducts?.length || 0,
        isLargeDataset: isLargeFile,
        metrics: datasetMetrics,
        enhancedImport: enhancedImportInfo
      };
    } catch (error) {
      console.error('Error importing data:', error);

      // Provide more specific error messages for large files
      let errorMessage = error.message;
      if (file.size > 500 * 1024 * 1024 && error.message.includes('memory')) {
        errorMessage = 'File too large for browser memory. Try using a smaller file or contact support.';
      }

      return {
        success: false,
        message: 'Error importing data: ' + errorMessage,
        fileSize: file.size
      };
    } finally {
      setProcessingProgress(0);
    }
  }, [initializeLargeDatasetTools, datasetMetrics]);

  /**
   * Get available backups
   */
  const getBackups = useCallback(() => {
    return dataStorage.getAvailableBackups();
  }, []);

  /**
   * Restore from backup
   */
  const restoreFromBackup = useCallback((backupKey) => {
    try {
      const restoredData = dataStorage.restoreFromBackup(backupKey);
      if (restoredData) {
        return {
          success: true,
          data: restoredData,
          message: 'Backup restored successfully'
        };
      } else {
        return { success: false, message: 'Failed to restore backup' };
      }
    } catch (error) {
      console.error('Error restoring backup:', error);
      return { success: false, message: 'Error restoring backup: ' + error.message };
    }
  }, []);

  /**
   * Delete a backup
   */
  const deleteBackup = useCallback((backupKey) => {
    try {
      const success = dataStorage.deleteBackup(backupKey);
      return {
        success,
        message: success ? 'Backup deleted successfully' : 'Failed to delete backup'
      };
    } catch (error) {
      console.error('Error deleting backup:', error);
      return { success: false, message: 'Error deleting backup: ' + error.message };
    }
  }, []);

  /**
   * Delete multiple backups
   */
  const deleteMultipleBackups = useCallback((backupKeys) => {
    try {
      const result = dataStorage.deleteMultipleBackups(backupKeys);
      return {
        success: result.success,
        message: result.success
          ? `${result.deletedCount} backup(s) deleted successfully`
          : `Failed to delete some backups: ${result.errors.join(', ')}`,
        deletedCount: result.deletedCount,
        errors: result.errors
      };
    } catch (error) {
      console.error('Error deleting multiple backups:', error);
      return { success: false, message: 'Error deleting backups: ' + error.message };
    }
  }, []);

  /**
   * Search products in large datasets
   */
  const searchProducts = useCallback(async (query, options = {}) => {
    if (!datasetManagerRef.current || !isLargeDataset) {
      // Fallback to simple search for small datasets
      return [];
    }

    try {
      const results = await datasetManagerRef.current.search(query, options);
      return results;
    } catch (error) {
      console.error('Error searching products:', error);
      return [];
    }
  }, [isLargeDataset]);

  /**
   * Get products by category efficiently
   */
  const getProductsByCategory = useCallback((category, limit = 1000) => {
    if (!datasetManagerRef.current || !isLargeDataset) {
      return [];
    }

    try {
      return datasetManagerRef.current.getProductsByCategory(category, limit);
    } catch (error) {
      console.error('Error getting products by category:', error);
      return [];
    }
  }, [isLargeDataset]);

  /**
   * Get performance metrics for large datasets
   */
  const getPerformanceMetrics = useCallback(() => {
    if (!datasetManagerRef.current) {
      return {};
    }

    const metrics = datasetManagerRef.current.getPerformanceMetrics();
    const memoryInfo = memoryMonitorRef.current?.getMemoryInfo();

    return {
      ...metrics,
      memoryInfo,
      processingProgress,
      isLargeDataset
    };
  }, [processingProgress, isLargeDataset]);

  /**
   * Force memory cleanup
   */
  const forceMemoryCleanup = useCallback(() => {
    if (datasetManagerRef.current) {
      datasetManagerRef.current.performMemoryCleanup();
    }
    if (memoryMonitorRef.current) {
      memoryMonitorRef.current.forceGC();
    }
  }, []);

  /**
   * Clear all data
   */
  const clearAllData = useCallback(() => {
    try {
      const success = dataStorage.clearAllData();
      if (success) {
        setHasLoadedData(false);
        setLastSaveTime(null);
        setStorageInfo(dataStorage.getStorageInfo());
      }
      return { 
        success, 
        message: success ? 'All data cleared successfully' : 'Failed to clear data' 
      };
    } catch (error) {
      console.error('Error clearing data:', error);
      return { success: false, message: 'Error clearing data: ' + error.message };
    }
  }, []);

  /**
   * Setup auto-save functionality
   */
  useEffect(() => {
    if (autoSaveEnabled && autoSaveIntervalRef.current === null) {
      // Start auto-save interval (every 5 minutes by default)
      autoSaveIntervalRef.current = setInterval(() => {
        // This will be triggered by the main app when data changes
        console.log('Auto-save interval triggered');
      }, 300000); // 5 minutes
    } else if (!autoSaveEnabled && autoSaveIntervalRef.current) {
      // Clear auto-save interval
      clearInterval(autoSaveIntervalRef.current);
      autoSaveIntervalRef.current = null;
    }

    return () => {
      if (autoSaveIntervalRef.current) {
        clearInterval(autoSaveIntervalRef.current);
      }
    };
  }, [autoSaveEnabled]);

  /**
   * Initialize large dataset tools on mount
   */
  useEffect(() => {
    initializeLargeDatasetTools();

    return () => {
      // Cleanup on unmount
      if (datasetManagerRef.current) {
        datasetManagerRef.current.destroy();
      }
      if (memoryMonitorRef.current) {
        memoryMonitorRef.current.stopMonitoring();
      }
      if (streamingParserRef.current) {
        streamingParserRef.current.abort();
      }
    };
  }, [initializeLargeDatasetTools]);

  /**
   * Update storage info and memory metrics periodically
   * Note: Initial data loading is now handled by App.jsx to avoid duplicate notifications
   */
  useEffect(() => {
    const updateStorageInfo = () => {
      setStorageInfo(dataStorage.getStorageInfo());
    };

    const updateMemoryMetrics = () => {
      if (memoryMonitorRef.current) {
        const memoryInfo = memoryMonitorRef.current.getMemoryInfo();
        if (memoryInfo) {
          setMemoryUsage(memoryInfo);
        }
      }
    };

    // Initial update
    updateStorageInfo();
    updateMemoryMetrics();

    // Set up periodic updates
    const interval = setInterval(() => {
      updateStorageInfo();
      updateMemoryMetrics();
    }, 60000); // Update every minute

    return () => {
      clearInterval(interval);
    };
  }, []);

  /**
   * Get enhanced storage status
   */
  const getEnhancedStorageStatus = useCallback(async () => {
    return await enhancedStorageManager.getStorageStatus();
  }, []);

  /**
   * Setup enhanced storage directory
   */
  const setupEnhancedStorage = useCallback(async () => {
    try {
      const result = await enhancedStorageManager.promptForDirectory();
      if (result.success) {
        const status = await enhancedStorageManager.getStorageStatus();
        setEnhancedStorageStatus(status);
        setStorageLocation(status.currentMode);
      }
      return result;
    } catch (error) {
      return {
        success: false,
        message: 'Failed to setup enhanced storage: ' + error.message
      };
    }
  }, []);

  const value = {
    // State
    isLoading,
    loadingMessage,
    hasLoadedData,
    autoSaveEnabled,
    lastSaveTime,
    storageInfo,

    // Enhanced storage state
    enhancedStorageStatus,
    autoRestoreStatus,
    storageLocation,

    // Large dataset state
    isLargeDataset,
    datasetMetrics,
    processingProgress,
    memoryUsage,

    // Methods
    loadApplicationData,
    saveApplicationData,
    saveUserPreferences,
    saveSettings,
    exportData,
    importData,
    getBackups,
    restoreFromBackup,
    deleteBackup,
    deleteMultipleBackups,
    clearAllData,

    // Enhanced storage methods
    getEnhancedStorageStatus,
    setupEnhancedStorage,

    // Large dataset methods
    searchProducts,
    getProductsByCategory,
    getPerformanceMetrics,
    forceMemoryCleanup,

    // Utilities
    setAutoSaveEnabled,
    dataStorage,
    enhancedStorageManager,

    // Large dataset managers (for advanced usage)
    datasetManager: datasetManagerRef.current,
    memoryMonitor: memoryMonitorRef.current
  };

  return (
    <DataContext.Provider value={value}>
      {children}
    </DataContext.Provider>
  );
};

export default DataContext;
