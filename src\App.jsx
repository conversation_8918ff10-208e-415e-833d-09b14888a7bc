import React, { useState, useRef, useMemo, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import Canvas from './components/Canvas';
import Toolbar from './components/Toolbar';
import UploadModal from './components/UploadModal';
import SettingsModal from './components/SettingsModal';
import StorageQuotaWarning from './components/StorageQuotaWarning';
import CameraStatusTest from './components/CameraStatusTest';
import DataLoadingIndicator from './components/DataLoadingIndicator';
import EnhancedDataLoader from './components/EnhancedDataLoader';
import { DataNotificationManager } from './components/DataNotification';

import { LanguageProvider } from './contexts/LanguageContext';
import { SettingsProvider, useSettings } from './contexts/SettingsContext';
import { DragProvider } from './contexts/DragContext';
import { DataProvider, useData } from './contexts/DataContext';
import { defaultProducts } from './data/products';
import { clearProductImageCache } from './utils/productImageUtils';
import autoDataSetup from './utils/autoDataSetup';

// Inner component that has access to SettingsContext and DataContext
function AppContent() {
  const { customProducts, addCustomProduct } = useSettings();
  const {
    isLoading: dataLoading,
    loadingMessage,
    loadApplicationData,
    saveApplicationData,
    hasLoadedData
  } = useData();

  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);
  const [showCameraTest, setShowCameraTest] = useState(false);
  const [dataLoadComplete, setDataLoadComplete] = useState(false);

  const [categoryRefreshKey, setCategoryRefreshKey] = useState(0);
  const [canvasProducts, setCanvasProducts] = useState([]);
  const [connections, setConnections] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [isConnectMode, setIsConnectMode] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const canvasRef = useRef(null);

  // Combine default products with custom products
  const allProducts = useMemo(() => {
    return [...defaultProducts, ...customProducts];
  }, [customProducts]);

  // Auto-load data on component mount
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize automatic data setup first
        console.log('Initializing automatic data setup...');
        const setupResult = await autoDataSetup.initialize();

        if (setupResult.success) {
          console.log('Automatic data setup completed:', setupResult.message);
        } else {
          console.warn('Automatic data setup failed:', setupResult.message);
        }

        // Clear PNG cache
        clearProductImageCache();
        console.log('Product image cache cleared for aspect ratio improvements');

        // Load application data
        const result = await loadApplicationData();

        if (result.success && result.data) {
          // Restore canvas state if available
          if (result.data.canvasProducts) {
            setCanvasProducts(result.data.canvasProducts);
          }
          if (result.data.connections) {
            setConnections(result.data.connections);
          }
          if (result.data.selectedProducts) {
            setSelectedProducts(result.data.selectedProducts);
          }

          // Show notification with enhanced information
          if (!result.isFirstTime) {
            setTimeout(() => {
              const message = result.isAppDataRestore
                ? 'Work restored from app storage'
                : result.isEnhancedRestore
                  ? 'Work restored from enhanced storage'
                  : result.message || 'Previous work restored';

              window.showDataNotification?.({
                type: 'success',
                message,
                action: 'load',
                duration: 3000,
                details: {
                  location: result.location,
                  enhanced: result.isEnhancedRestore || result.isAppDataRestore
                }
              });
            }, 1000);
          }
        }

        setDataLoadComplete(true);

      } catch (error) {
        console.error('App initialization error:', error);
        setDataLoadComplete(true); // Still mark as complete to prevent hanging
      }
    };

    initializeApp();
  }, [loadApplicationData]);

  // Auto-save when canvas data changes
  useEffect(() => {
    if (!dataLoadComplete) return; // Don't save during initial load

    const saveData = async () => {
      const dataToSave = {
        canvasProducts,
        connections,
        selectedProducts,
        metadata: {
          version: '1.0.0',
          lastModified: new Date().toISOString(),
          totalProducts: canvasProducts.length,
          totalConnections: connections.length
        }
      };

      const result = await saveApplicationData(dataToSave);

      if (result.success) {
        console.log('Auto-save completed successfully');
      } else {
        console.warn('Auto-save failed:', result.message);
      }
    };

    // Debounce auto-save to avoid too frequent saves
    const timeoutId = setTimeout(saveData, 2000); // Save 2 seconds after last change

    return () => clearTimeout(timeoutId);
  }, [canvasProducts, connections, selectedProducts, dataLoadComplete, saveApplicationData]);

  // Add product to canvas
  const addProductToCanvas = (product, position = null) => {
    const newProduct = {
      ...product,
      id: `${product.id}-${Date.now()}`,
      position: position || {
        x: Math.random() * 300 + 50,
        y: Math.random() * 200 + 50
      },
      rotation: 0 // Initialize rotation to 0 degrees
    };

    setCanvasProducts(prev => [...prev, newProduct]);
    saveToHistory([...canvasProducts, newProduct], connections);
  };

  // Remove product from canvas
  const removeProductFromCanvas = (productId) => {
    const newProducts = canvasProducts.filter(p => p.id !== productId);
    const newConnections = connections.filter(c => c.from !== productId && c.to !== productId);

    setCanvasProducts(newProducts);
    setConnections(newConnections);
    setSelectedProducts(prev => prev.filter(id => id !== productId));
    saveToHistory(newProducts, newConnections);
  };

  // Move product on canvas with optimized updates
  const moveProduct = (productId, position) => {
    setCanvasProducts(prevProducts =>
      prevProducts.map(p =>
        p.id === productId ? { ...p, position } : p
      )
    );
  };

  // Rotate product on canvas
  const rotateProduct = (productId) => {
    const newProducts = canvasProducts.map(p => {
      if (p.id === productId) {
        const currentRotation = p.rotation || 0;
        const newRotation = (currentRotation + 90) % 360;
        return { ...p, rotation: newRotation };
      }
      return p;
    });
    setCanvasProducts(newProducts);
    saveToHistory(newProducts, connections);
  };

  // Handle product selection
  const handleProductSelect = (productId, isCtrlPressed = false) => {
    if (isConnectMode) {
      handleConnection(productId);
      return;
    }

    if (isCtrlPressed) {
      setSelectedProducts(prev =>
        prev.includes(productId)
          ? prev.filter(id => id !== productId)
          : [...prev, productId]
      );
    } else {
      setSelectedProducts([productId]);
    }
  };

  // Handle connections in connect mode
  const handleConnection = (productId) => {
    if (selectedProducts.length === 0) {
      setSelectedProducts([productId]);
    } else if (selectedProducts.length === 1 && selectedProducts[0] !== productId) {
      const newConnection = {
        id: `connection-${Date.now()}`,
        from: selectedProducts[0],
        to: productId
      };
      setConnections(prev => [...prev, newConnection]);
      setSelectedProducts([]);
      saveToHistory(canvasProducts, [...connections, newConnection]);
    } else {
      setSelectedProducts([productId]);
    }
  };

  // Save state to history for undo/redo
  const saveToHistory = (products, conns) => {
    const newState = { products: [...products], connections: [...conns] };
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newState);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  // Undo functionality
  const handleUndo = () => {
    if (historyIndex > 0) {
      const prevState = history[historyIndex - 1];
      setCanvasProducts(prevState.products);
      setConnections(prevState.connections);
      setHistoryIndex(historyIndex - 1);
    }
  };

  // Redo functionality
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const nextState = history[historyIndex + 1];
      setCanvasProducts(nextState.products);
      setConnections(nextState.connections);
      setHistoryIndex(historyIndex + 1);
    }
  };

  // Clear canvas
  const handleClear = () => {
    setCanvasProducts([]);
    setConnections([]);
    setSelectedProducts([]);
    saveToHistory([], []);
  };

  // Toggle connect mode
  const toggleConnectMode = () => {
    setIsConnectMode(!isConnectMode);
    setSelectedProducts([]);
  };

  // Toggle selection mode for export
  const toggleSelectionMode = () => {
    setIsSelectionMode(!isSelectionMode);
    setSelectedProducts([]);
  };

  // Handle selection mode actions
  const handleSelectionConfirm = (selectionRect) => {
    // This will be called from the Toolbar component
    setIsSelectionMode(false);
    return selectionRect;
  };

  const handleSelectionCancel = () => {
    setIsSelectionMode(false);
  };

  const handleSelectionClear = () => {
    // Clear current selection but stay in selection mode
  };

  // Handle data import from toolbar
  const handleDataImport = (importedData) => {
    // Force refresh of category-dependent components
    setCategoryRefreshKey(prev => prev + 1);

    // If the imported data contains saved projects with canvas data, we could load the first one
    // This is optional - you might want to show a project selection dialog instead
    if (importedData.savedProjects && importedData.savedProjects.length > 0) {
      const firstProject = importedData.savedProjects[0];
      if (firstProject.data && firstProject.data.products) {
        setCanvasProducts(firstProject.data.products || []);
        setConnections(firstProject.data.connections || []);
        saveToHistory(firstProject.data.products || [], firstProject.data.connections || []);
      }
    }
  };

  // Check URL for camera test mode
  useEffect(() => {
    if (window.location.search.includes('camera-test')) {
      setShowCameraTest(true);
    }
  }, []);

  if (showCameraTest) {
    return (
      <div className="min-h-screen bg-gray-100 p-4">
        <div className="mb-4">
          <button
            onClick={() => setShowCameraTest(false)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            ← Back to Main App
          </button>
        </div>
        <CameraStatusTest />
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Data Loading Indicator */}
      <DataLoadingIndicator
        isLoading={dataLoading}
        message={loadingMessage}
        type="loading"
      />

      {/* Data Notification Manager */}
      <DataNotificationManager />

      {/* Toolbar */}
      <Toolbar
        onUndo={handleUndo}
        onRedo={handleRedo}
        onClear={handleClear}
        onToggleConnect={toggleConnectMode}
        isConnectMode={isConnectMode}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < history.length - 1}
        canvasProducts={canvasProducts}
        connections={connections}
        canvasRef={canvasRef}
        onOpenSettings={() => setShowSettingsModal(true)}
        isSelectionMode={isSelectionMode}
        onToggleSelectionMode={toggleSelectionMode}
        onSelectionConfirm={handleSelectionConfirm}
        onSelectionCancel={handleSelectionCancel}
        onSelectionClear={handleSelectionClear}
        onDataImport={handleDataImport}
      />

      {/* Main Content */}
      <div className="flex flex-1">
        <Sidebar
          key={categoryRefreshKey}
          products={allProducts}
          onUploadClick={() => setShowUploadModal(true)}
          onProductDragStart={addProductToCanvas}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
        />

        <Canvas
          ref={canvasRef}
          products={canvasProducts}
          connections={connections}
          selectedProducts={selectedProducts}
          isConnectMode={isConnectMode}
          onProductSelect={handleProductSelect}
          onProductMove={moveProduct}
          onProductRemove={removeProductFromCanvas}
          onProductRotate={rotateProduct}
          onProductDrop={addProductToCanvas}
          isSelectionMode={isSelectionMode}
          onSelectionConfirm={handleSelectionConfirm}
          onSelectionCancel={handleSelectionCancel}
          onSelectionClear={handleSelectionClear}
        />
      </div>

      {/* Modals */}
      {showUploadModal && (
        <UploadModal
          onClose={() => setShowUploadModal(false)}
          onSubmit={(data) => {
            try {
              // Add the custom product using the SettingsContext
              const newProduct = addCustomProduct(data);
              console.log('Product added successfully:', newProduct);
              setShowUploadModal(false);
            } catch (error) {
              console.error('Error adding product:', error);
              // Don't close modal if there's an error
            }
          }}
        />
      )}

      {showSettingsModal && (
        <SettingsModal
          isOpen={showSettingsModal}
          onClose={() => setShowSettingsModal(false)}
          canvasProducts={canvasProducts}
          connections={connections}
          onLoadProject={(data) => {
            setCanvasProducts(data.products || []);
            setConnections(data.connections || []);
            saveToHistory(data.products || [], data.connections || []);
          }}
          onClearWorkspace={handleClear}
          onCategoryChanges={() => {
            // Force refresh of category-dependent components
            setCategoryRefreshKey(prev => prev + 1);
          }}
        />
      )}

      {/* Storage Quota Warning */}
      <StorageQuotaWarning />
    </div>
  );
}

// Main App component with providers
function App() {
  const handleDataLoaded = (loadedData) => {
    console.log('Enhanced data loader completed:', loadedData);
    // Additional initialization logic can be added here
  };

  const handleDataLoadError = (error) => {
    console.error('Enhanced data loader failed:', error);
    // Error handling logic can be added here
  };

  return (
    <LanguageProvider>
      <SettingsProvider>
        <DataProvider>
          <DragProvider>
            <EnhancedDataLoader
              onDataLoaded={handleDataLoaded}
              onError={handleDataLoadError}
            >
              <AppContent />
            </EnhancedDataLoader>
          </DragProvider>
        </DataProvider>
      </SettingsProvider>
    </LanguageProvider>
  );
}

export default App;

