/**
 * Auto Data Setup for PlomDesign
 * Automatically creates and manages the app's data storage structure on startup
 */

import appDataManager from './appDataManager';

class AutoDataSetup {
  constructor() {
    this.isSetupComplete = false;
    this.setupPromise = null;
  }

  /**
   * Initialize the automatic data setup
   */
  async initialize() {
    if (this.setupPromise) {
      return this.setupPromise;
    }

    this.setupPromise = this.performSetup();
    return this.setupPromise;
  }

  /**
   * Perform the actual setup
   */
  async performSetup() {
    try {
      console.log('AutoDataSetup: Starting automatic data setup...');

      // Check if setup has already been completed
      const existingSetup = localStorage.getItem('plom_auto_setup_complete');
      if (existingSetup) {
        const setupData = JSON.parse(existingSetup);
        if (this.isSetupStillValid(setupData)) {
          console.log('AutoDataSetup: Existing setup is valid, skipping...');
          this.isSetupComplete = true;
          return { success: true, message: 'Setup already complete', existing: true };
        }
      }

      // Initialize app data manager
      const appDataStatus = appDataManager.getStatus();
      if (!appDataStatus.initialized) {
        console.log('AutoDataSetup: App data manager not initialized, waiting...');
        // Wait a bit for initialization
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Create initial data structure
      await this.createInitialDataStructure();

      // Set up automatic data upload/sync
      await this.setupAutoDataUpload();

      // Mark setup as complete
      const setupInfo = {
        completed: new Date().toISOString(),
        version: '1.0.0',
        features: {
          appDataManager: true,
          autoUpload: true,
          autoBackup: true
        }
      };

      localStorage.setItem('plom_auto_setup_complete', JSON.stringify(setupInfo));
      this.isSetupComplete = true;

      console.log('AutoDataSetup: Automatic data setup completed successfully');
      return { 
        success: true, 
        message: 'Automatic data setup completed successfully',
        setupInfo 
      };

    } catch (error) {
      console.error('AutoDataSetup: Setup failed:', error);
      return { 
        success: false, 
        message: 'Automatic data setup failed: ' + error.message,
        error: error.message 
      };
    }
  }

  /**
   * Check if existing setup is still valid
   */
  isSetupStillValid(setupData) {
    try {
      // Check if setup was completed recently (within last 7 days)
      const setupDate = new Date(setupData.completed);
      const daysSinceSetup = (Date.now() - setupDate.getTime()) / (1000 * 60 * 60 * 24);
      
      if (daysSinceSetup > 7) {
        return false; // Re-setup after 7 days
      }

      // Check if app data manager is still working
      const appDataStatus = appDataManager.getStatus();
      if (!appDataStatus.available) {
        return false;
      }

      return true;
    } catch (error) {
      console.warn('AutoDataSetup: Error checking setup validity:', error);
      return false;
    }
  }

  /**
   * Create initial data structure
   */
  async createInitialDataStructure() {
    try {
      console.log('AutoDataSetup: Creating initial data structure...');

      // Create welcome/info file in the workspace
      const welcomeData = {
        appName: 'PlomDesign',
        version: '1.0.0',
        welcome: 'Welcome to PlomDesign! Your data is automatically managed and backed up.',
        features: [
          'Automatic data saving and loading',
          'Backup creation on every save',
          'Enhanced storage support',
          'Large file handling',
          'Real-time data synchronization'
        ],
        dataStructure: {
          workspace: 'Contains your current work and active projects',
          backups: 'Automatic backups of your work',
          exports: 'Data exports and downloads',
          imports: 'Imported data and uploads',
          settings: 'App configuration and preferences',
          cache: 'Temporary files and cached data',
          logs: 'Application logs and debug information'
        },
        created: new Date().toISOString()
      };

      await appDataManager.saveToFolder('workspace', 'welcome.json', welcomeData);

      // Create initial settings
      const initialSettings = {
        autoSave: true,
        autoLoad: true,
        autoBackup: true,
        backupInterval: 300000, // 5 minutes
        maxBackups: 20,
        dataUploadEnabled: true,
        created: new Date().toISOString()
      };

      await appDataManager.saveToFolder('settings', 'app_settings.json', initialSettings);

      // Create a sample log entry
      const initialLog = {
        timestamp: new Date().toISOString(),
        level: 'INFO',
        message: 'PlomDesign data storage initialized successfully',
        source: 'AutoDataSetup',
        details: {
          appDataManager: 'initialized',
          folders: 'created',
          settings: 'configured'
        }
      };

      await appDataManager.saveToFolder('logs', `setup_${Date.now()}.json`, initialLog);

      console.log('AutoDataSetup: Initial data structure created');
      return true;

    } catch (error) {
      console.error('AutoDataSetup: Failed to create initial data structure:', error);
      throw error;
    }
  }

  /**
   * Set up automatic data upload functionality
   */
  async setupAutoDataUpload() {
    try {
      console.log('AutoDataSetup: Setting up automatic data upload...');

      // Check if there's existing data to upload
      const existingData = this.checkForExistingData();
      
      if (existingData.hasData) {
        console.log('AutoDataSetup: Found existing data, uploading to app storage...');
        await this.uploadExistingData(existingData);
      }

      // Set up periodic data sync
      this.setupPeriodicSync();

      console.log('AutoDataSetup: Automatic data upload configured');
      return true;

    } catch (error) {
      console.error('AutoDataSetup: Failed to setup auto data upload:', error);
      throw error;
    }
  }

  /**
   * Check for existing data in localStorage
   */
  checkForExistingData() {
    try {
      const canvasState = localStorage.getItem('plom_canvas_state');
      const settings = localStorage.getItem('plom_settings');
      const userPreferences = localStorage.getItem('plom_user_preferences');

      const hasData = !!(canvasState || settings || userPreferences);

      return {
        hasData,
        canvasState: canvasState ? JSON.parse(canvasState) : null,
        settings: settings ? JSON.parse(settings) : null,
        userPreferences: userPreferences ? JSON.parse(userPreferences) : null
      };

    } catch (error) {
      console.warn('AutoDataSetup: Error checking existing data:', error);
      return { hasData: false };
    }
  }

  /**
   * Upload existing data to app storage
   */
  async uploadExistingData(existingData) {
    try {
      if (existingData.canvasState) {
        await appDataManager.saveToFolder('workspace', 'migrated_canvas_state.json', {
          ...existingData.canvasState,
          migrated: new Date().toISOString(),
          source: 'localStorage'
        });
      }

      if (existingData.settings) {
        await appDataManager.saveToFolder('settings', 'migrated_settings.json', {
          ...existingData.settings,
          migrated: new Date().toISOString(),
          source: 'localStorage'
        });
      }

      if (existingData.userPreferences) {
        await appDataManager.saveToFolder('settings', 'migrated_preferences.json', {
          ...existingData.userPreferences,
          migrated: new Date().toISOString(),
          source: 'localStorage'
        });
      }

      // Log the migration
      const migrationLog = {
        timestamp: new Date().toISOString(),
        level: 'INFO',
        message: 'Existing data migrated to app storage',
        source: 'AutoDataSetup',
        details: {
          canvasState: !!existingData.canvasState,
          settings: !!existingData.settings,
          userPreferences: !!existingData.userPreferences
        }
      };

      await appDataManager.saveToFolder('logs', `migration_${Date.now()}.json`, migrationLog);

      console.log('AutoDataSetup: Existing data uploaded successfully');

    } catch (error) {
      console.error('AutoDataSetup: Failed to upload existing data:', error);
      throw error;
    }
  }

  /**
   * Set up periodic data synchronization
   */
  setupPeriodicSync() {
    // Set up a periodic check for data synchronization
    setInterval(async () => {
      try {
        // Check if app data manager needs cleanup
        const status = appDataManager.getStatus();
        if (status.initialized) {
          // Perform periodic cleanup (every hour)
          await appDataManager.cleanup();
        }
      } catch (error) {
        console.warn('AutoDataSetup: Periodic sync error:', error);
      }
    }, 60 * 60 * 1000); // Every hour

    console.log('AutoDataSetup: Periodic sync configured');
  }

  /**
   * Get setup status
   */
  getSetupStatus() {
    return {
      isSetupComplete: this.isSetupComplete,
      appDataManager: appDataManager.getStatus(),
      lastSetup: localStorage.getItem('plom_auto_setup_complete')
    };
  }

  /**
   * Force re-setup
   */
  async forceResetup() {
    localStorage.removeItem('plom_auto_setup_complete');
    this.isSetupComplete = false;
    this.setupPromise = null;
    return this.initialize();
  }
}

// Create and export singleton instance
const autoDataSetup = new AutoDataSetup();
export default autoDataSetup;
