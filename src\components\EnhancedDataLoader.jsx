/**
 * Enhanced Data Loader Component
 * Handles automatic data loading and initialization on app startup
 * with improved user feedback and error handling
 */

import React, { useState, useEffect } from 'react';
import { Database, CheckCircle, AlertCircle, RefreshCw, HardDrive } from 'lucide-react';
import { centralizedStorage } from '../utils/centralizedDataStorage';

const EnhancedDataLoader = ({ onDataLoaded, onError, children }) => {
  const [loadingState, setLoadingState] = useState({
    isLoading: true,
    currentStep: 'initializing',
    progress: 0,
    message: 'Initializing data storage...',
    error: null,
    loadedData: null
  });

  const [loadingSteps] = useState([
    { id: 'initializing', label: 'Initializing storage system', weight: 10 },
    { id: 'checking', label: 'Checking data availability', weight: 20 },
    { id: 'loading', label: 'Loading workspace data', weight: 30 },
    { id: 'validating', label: 'Validating data integrity', weight: 20 },
    { id: 'finalizing', label: 'Finalizing setup', weight: 20 }
  ]);

  useEffect(() => {
    initializeDataLoading();
  }, []);

  const initializeDataLoading = async () => {
    try {
      // Step 1: Initialize storage system
      await updateLoadingState('initializing', 'Initializing centralized storage...');
      const storageInitialized = await centralizedStorage.initialize();
      
      if (!storageInitialized) {
        throw new Error('Failed to initialize storage system');
      }

      // Step 2: Check data availability
      await updateLoadingState('checking', 'Checking for existing data...');
      const dataAvailability = await checkDataAvailability();

      // Step 3: Load data
      await updateLoadingState('loading', 'Loading workspace data...');
      const loadedData = await loadWorkspaceData(dataAvailability);

      // Step 4: Validate data
      await updateLoadingState('validating', 'Validating data integrity...');
      const validationResult = await validateLoadedData(loadedData);

      if (!validationResult.isValid) {
        console.warn('Data validation issues:', validationResult.issues);
      }

      // Step 5: Finalize
      await updateLoadingState('finalizing', 'Finalizing setup...');
      await finalizeDataLoading(loadedData);

      // Complete loading
      setLoadingState(prev => ({
        ...prev,
        isLoading: false,
        progress: 100,
        message: 'Data loaded successfully!',
        loadedData
      }));

      // Notify parent component
      if (onDataLoaded) {
        onDataLoaded(loadedData);
      }

    } catch (error) {
      console.error('Data loading failed:', error);
      setLoadingState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message,
        message: 'Failed to load data'
      }));

      if (onError) {
        onError(error);
      }
    }
  };

  const updateLoadingState = async (step, message) => {
    const stepIndex = loadingSteps.findIndex(s => s.id === step);
    const progress = loadingSteps
      .slice(0, stepIndex + 1)
      .reduce((sum, s) => sum + s.weight, 0);

    setLoadingState(prev => ({
      ...prev,
      currentStep: step,
      progress,
      message
    }));

    // Simulate realistic loading time
    await new Promise(resolve => setTimeout(resolve, 300));
  };

  const checkDataAvailability = async () => {
    const availability = {
      hasWorkspace: false,
      hasBackups: false,
      hasSettings: false,
      lastSaveTime: null
    };

    try {
      // Check for workspace data
      const workspaceResult = await centralizedStorage.loadData('workspace_current');
      availability.hasWorkspace = workspaceResult.success;

      // Check for backups
      const backupIndex = await centralizedStorage.loadData('backup_index');
      availability.hasBackups = backupIndex.success && backupIndex.data?.backups?.length > 0;

      // Check for settings
      const settingsResult = await centralizedStorage.loadData('app_settings');
      availability.hasSettings = settingsResult.success;

      // Get last save time
      const lastSaveResult = await centralizedStorage.loadData('last_save');
      availability.lastSaveTime = lastSaveResult.success ? lastSaveResult.data : null;

    } catch (error) {
      console.warn('Error checking data availability:', error);
    }

    return availability;
  };

  const loadWorkspaceData = async (availability) => {
    const loadedData = {
      workspace: null,
      settings: null,
      backups: [],
      metadata: {
        loadedAt: new Date().toISOString(),
        source: 'none'
      }
    };

    try {
      // Load workspace data if available
      if (availability.hasWorkspace) {
        const workspaceResult = await centralizedStorage.loadData('workspace_current');
        if (workspaceResult.success) {
          loadedData.workspace = workspaceResult.data;
          loadedData.metadata.source = 'workspace';
        }
      }

      // Load settings
      if (availability.hasSettings) {
        const settingsResult = await centralizedStorage.loadData('app_settings');
        if (settingsResult.success) {
          loadedData.settings = settingsResult.data;
        }
      }

      // Load backup information
      if (availability.hasBackups) {
        const backupResult = await centralizedStorage.loadData('backup_index');
        if (backupResult.success) {
          loadedData.backups = backupResult.data.backups || [];
        }
      }

    } catch (error) {
      console.warn('Error loading workspace data:', error);
    }

    return loadedData;
  };

  const validateLoadedData = async (data) => {
    const validation = {
      isValid: true,
      issues: []
    };

    try {
      // Validate workspace data structure
      if (data.workspace) {
        if (!data.workspace.canvasProducts) {
          validation.issues.push('Missing canvas products array');
        }
        if (!data.workspace.connections) {
          validation.issues.push('Missing connections array');
        }
      }

      // Validate settings structure
      if (data.settings) {
        if (!data.settings.version) {
          validation.issues.push('Missing settings version');
        }
      }

      validation.isValid = validation.issues.length === 0;

    } catch (error) {
      validation.isValid = false;
      validation.issues.push(`Validation error: ${error.message}`);
    }

    return validation;
  };

  const finalizeDataLoading = async (data) => {
    try {
      // Update session information
      const sessionInfo = {
        lastDataLoad: new Date().toISOString(),
        dataSource: data.metadata.source,
        hasWorkspace: !!data.workspace,
        hasSettings: !!data.settings,
        backupCount: data.backups.length
      };

      await centralizedStorage.saveData('session_info', sessionInfo);

    } catch (error) {
      console.warn('Error finalizing data loading:', error);
    }
  };

  const retryLoading = () => {
    setLoadingState({
      isLoading: true,
      currentStep: 'initializing',
      progress: 0,
      message: 'Retrying data loading...',
      error: null,
      loadedData: null
    });
    initializeDataLoading();
  };

  // Show loading screen while data is being loaded
  if (loadingState.isLoading || loadingState.error) {
    return (
      <div className="fixed inset-0 bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 border border-gray-200">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mb-4">
              {loadingState.error ? (
                <AlertCircle className="w-8 h-8 text-red-600" />
              ) : loadingState.progress === 100 ? (
                <CheckCircle className="w-8 h-8 text-green-600" />
              ) : (
                <Database className="w-8 h-8 text-blue-600" />
              )}
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              {loadingState.error ? 'Loading Failed' : 'Loading PlomDesign'}
            </h2>
            <p className="text-gray-600">
              {loadingState.error ? 'An error occurred while loading your data' : 'Preparing your workspace...'}
            </p>
          </div>

          {/* Progress or Error */}
          {loadingState.error ? (
            <div className="space-y-4">
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-red-800 text-sm">{loadingState.error}</p>
              </div>
              <button
                onClick={retryLoading}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Retry Loading</span>
              </button>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">{loadingState.message}</span>
                  <span className="text-blue-600 font-medium">{loadingState.progress}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${loadingState.progress}%` }}
                  />
                </div>
              </div>

              {/* Loading Steps */}
              <div className="space-y-2">
                {loadingSteps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center space-x-3 text-sm ${
                      loadingState.currentStep === step.id
                        ? 'text-blue-600 font-medium'
                        : loadingSteps.findIndex(s => s.id === loadingState.currentStep) > index
                        ? 'text-green-600'
                        : 'text-gray-400'
                    }`}
                  >
                    <div className={`w-2 h-2 rounded-full ${
                      loadingState.currentStep === step.id
                        ? 'bg-blue-600'
                        : loadingSteps.findIndex(s => s.id === loadingState.currentStep) > index
                        ? 'bg-green-600'
                        : 'bg-gray-300'
                    }`} />
                    <span>{step.label}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Render children when loading is complete
  return children;
};

export default EnhancedDataLoader;
